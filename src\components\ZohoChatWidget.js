import React, { useEffect } from 'react';
 
declare global {
  interface Window {
    $zoho: any;
  }
}
 
function clearZohoSalesIQCookies() {
  const cookies = document.cookie.split(';');
  cookies.forEach(cookie => {
    const name = cookie.split('=')[0].trim();
    if (name.startsWith('zsiq_')) {
      document.cookie = `${name}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
    }
  });
}
 
interface ZohoChatWidgetProps {
  name: string;
  role: string;
  department: string;
}
 
const WIDGETS: Record<string, { widgetcode: string; scriptUrl: string }> = {
  Admin: {
    widgetcode: 'siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076', // Replace with your Admin widget code
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=siqb95d6ef899c13858d7f5f37fa6171acbe5201291a5ec9676108f4bb3fd896076',
  },
  User: {
    widgetcode: 'siqb95d6ef899c13858d7f5f37fa6171acb13781a53e1b74032df8007af1fa7f9b1', // Replace with your User widget code
    scriptUrl: 'https://salesiq.zohopublic.in/widget?wc=siqb95d6ef899c13858d7f5f37fa6171acb13781a53e1b74032df8007af1fa7f9b1',
  },
  // Add more roles if needed
};
 
 
const ZohoChatWidget: React.FC<ZohoChatWidgetProps> = ({ name, role, department }) => {
  useEffect(() => {
    clearZohoSalesIQCookies();
 
    // Remove any existing widget script before adding a new one
    const oldScript = document.getElementById('zsiqscript');
    if (oldScript) {
      oldScript.remove();
    }
 
    // Pick widget config based on role, fallback to 'User'
    const widget = WIDGETS[role];
 
    window.$zoho = window.$zoho || {};
    window.$zoho.salesiq = {
      widgetcode: widget.widgetcode,
      values: {},
      ready: function () {
        window.$zoho.salesiq.visitor.name(name);
        window.$zoho.salesiq.visitor.info({
          "Role": role,
          "Name": name,
        });
        //window.$zoho.salesiq.chat && window.$zoho.salesiq.chat.department && window.$zoho.salesiq.chat.department(department);
      },
    };
 
    // Inject the correct Zoho SalesIQ script
    const script = document.createElement('script');
    script.type = 'text/javascript';
    script.defer = true;
    script.id = 'zsiqscript';
    script.src = widget.scriptUrl;
    
    document.body.appendChild(script);
 
    // No cleanup needed if widget should persist
 
  }, [name, role, department]);
 
  return null;
};
 
export default ZohoChatWidget;